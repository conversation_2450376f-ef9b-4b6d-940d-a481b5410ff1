package com.example.castapp.manager.windowsettings

import androidx.core.view.isVisible
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.manager.StateManager
import com.example.castapp.utils.AppLog

/**
 * 窗口信息模块
 * 负责提供窗口信息和状态查询服务
 */
class WindowInfoModule(
    private val dataModule: WindowDataModule
) {

    /**
     * 创建媒体窗口信息（视频/图片）
     */
    private fun createMediaWindowInfo(mediaId: String, mediaType: String, transformHandler: com.example.castapp.ui.windowsettings.TransformHandler, zOrder: Int): CastWindowInfo {
        // 获取变换信息（🎯 修复：使用容器位置而不是可见区域位置）
        val positionX = transformHandler.getContainerDisplayX()
        val positionY = transformHandler.getContainerDisplayY()
        val scaleFactor = transformHandler.getCurrentScaleFactor()
        val rotationAngle = transformHandler.getCurrentRotation()

        // 获取裁剪区域参数和裁剪模式状态
        val cropRectRatio = transformHandler.getCropRectRatio()
        val isCropping = transformHandler.isCroppingMode()

        // 获取真实的变换功能开关状态
        val isDragEnabled = transformHandler.isDragEnabled()
        val isScaleEnabled = transformHandler.isScaleEnabled()
        val isRotationEnabled = transformHandler.isRotationEnabled()
        val isVisible = transformHandler.isVisible
        val isMirrored = transformHandler.isMirrorEnabled()


        // 🎯 修复：媒体窗口现在支持精准调控，从DataModule获取调控状态
        val isControlEnabled = dataModule.getControlState(mediaId)

        // 获取UI效果参数
        val cornerRadius = transformHandler.getCornerRadius()
        val alpha = transformHandler.getWindowAlpha()
        val isBorderEnabled = transformHandler.isBorderEnabled()
        val borderColor = transformHandler.getBorderColor()
        val borderWidth = transformHandler.getBorderWidth()

        // 获取窗口尺寸
        val baseWindowWidth = transformHandler.getBaseWindowWidth()
        val baseWindowHeight = transformHandler.getBaseWindowHeight()

        // 🏷️ 获取备注信息
        val activity = dataModule.getCurrentActivity()
        val noteManager = com.example.castapp.utils.NoteManager(activity!!)
        val note = noteManager.getNote(mediaId)

        return CastWindowInfo(
            connectionId = mediaId,
            ipAddress = "本地设备",
            port = 0,
            isActive = true,
            deviceName = mediaType,
            note = note,
            positionX = positionX,
            positionY = positionY,
            scaleFactor = scaleFactor,
            rotationAngle = rotationAngle,
            zOrder = zOrder,
            isCropping = isCropping,
            cropRectRatio = cropRectRatio,
            isDragEnabled = isDragEnabled,
            isScaleEnabled = isScaleEnabled,
            isRotationEnabled = isRotationEnabled,
            isVisible = isVisible,
            isMirrored = isMirrored,
            cornerRadius = cornerRadius,
            alpha = alpha,
            isControlEnabled = isControlEnabled,
            isEditEnabled = false, // 📝 媒体窗口不支持编辑功能

            isBorderEnabled = isBorderEnabled,
            borderColor = borderColor,
            borderWidth = borderWidth,
            baseWindowWidth = baseWindowWidth,
            baseWindowHeight = baseWindowHeight,
            isLandscapeModeEnabled = false // 🎯 媒体窗口不支持横屏模式
        )
    }

    /**
     * 创建文本窗口信息
     */
    private fun createTextWindowInfo(textId: String, textContent: String, transformHandler: com.example.castapp.ui.windowsettings.TransformHandler, zOrder: Int): CastWindowInfo {
        // 获取变换信息（🎯 修复：使用容器位置而不是可见区域位置）
        val positionX = transformHandler.getContainerDisplayX()
        val positionY = transformHandler.getContainerDisplayY()
        val scaleFactor = transformHandler.getCurrentScaleFactor()
        val rotationAngle = transformHandler.getCurrentRotation()

        // 获取裁剪区域参数和裁剪模式状态（文本窗口不支持裁剪，但保持接口一致性）
        val cropRectRatio = null // 文本窗口不支持裁剪
        val isCropping = false

        // 获取真实的变换功能开关状态
        val isDragEnabled = transformHandler.isDragEnabled()
        val isScaleEnabled = transformHandler.isScaleEnabled()
        val isRotationEnabled = transformHandler.isRotationEnabled()
        val isVisible = transformHandler.isVisible
        val isMirrored = false // 文本窗口不支持镜像


        // 文本窗口支持精准调控
        val isControlEnabled = dataModule.getControlState(textId)

        // 📝 文本窗口支持编辑功能
        val isEditEnabled = dataModule.getEditState(textId)

        // 获取UI效果参数
        val cornerRadius = transformHandler.getCornerRadius()
        val alpha = transformHandler.getWindowAlpha()
        val isBorderEnabled = transformHandler.isBorderEnabled()
        val borderColor = transformHandler.getBorderColor()
        val borderWidth = transformHandler.getBorderWidth()

        // 获取窗口尺寸
        val baseWindowWidth = transformHandler.getBaseWindowWidth()
        val baseWindowHeight = transformHandler.getBaseWindowHeight()

        // 🏷️ 获取备注信息
        val activity = dataModule.getCurrentActivity()
        val noteManager = com.example.castapp.utils.NoteManager(activity!!)
        val note = noteManager.getNote(textId)

        // 🎨 获取窗口背景颜色信息
        var windowColorEnabled = false
        var windowBackgroundColor = 0xFFFFFFFF.toInt()

        try {
            val textWindowView = transformHandler.getTextWindowManager()?.getTextWindowView()
            if (textWindowView != null) {
                val colorState = textWindowView.getWindowBackgroundColorState()
                windowColorEnabled = colorState.first
                windowBackgroundColor = colorState.second
                AppLog.d("【窗口信息模块】获取文本窗口背景颜色: 启用=$windowColorEnabled, 颜色=${String.format("#%08X", windowBackgroundColor)}")
            }
        } catch (e: Exception) {
            AppLog.w("【窗口信息模块】获取文本窗口背景颜色失败", e)
        }

        return CastWindowInfo(
            connectionId = textId,
            ipAddress = "本地设备",
            port = 0,
            isActive = true,
            deviceName = textContent, // 设备名称显示实际文本内容
            note = note,
            positionX = positionX,
            positionY = positionY,
            scaleFactor = scaleFactor,
            rotationAngle = rotationAngle,
            zOrder = zOrder,
            isCropping = isCropping,
            cropRectRatio = cropRectRatio,
            isDragEnabled = isDragEnabled,
            isScaleEnabled = isScaleEnabled,
            isRotationEnabled = isRotationEnabled,
            isVisible = isVisible,
            isMirrored = isMirrored,
            cornerRadius = cornerRadius,
            alpha = alpha,
            isControlEnabled = isControlEnabled,
            isEditEnabled = isEditEnabled,

            isBorderEnabled = isBorderEnabled,
            borderColor = borderColor,
            borderWidth = borderWidth,
            baseWindowWidth = baseWindowWidth,
            baseWindowHeight = baseWindowHeight,
            windowColorEnabled = windowColorEnabled, // 🎨 窗口背景颜色启用状态
            windowBackgroundColor = windowBackgroundColor, // 🎨 窗口背景颜色
            isLandscapeModeEnabled = false // 🎯 文本窗口不支持横屏模式
        )
    }

    /**
     * 创建摄像头窗口信息
     */
    private fun createCameraWindowInfo(cameraId: String, cameraName: String, transformHandler: com.example.castapp.ui.windowsettings.TransformHandler, zOrder: Int): CastWindowInfo {
        // 获取变换信息（🎯 修复：使用容器位置而不是可见区域位置）
        val positionX = transformHandler.getContainerDisplayX()
        val positionY = transformHandler.getContainerDisplayY()
        val scaleFactor = transformHandler.getCurrentScaleFactor()
        val rotationAngle = transformHandler.getCurrentRotation()

        // 获取裁剪区域参数和裁剪模式状态
        val cropRectRatio = transformHandler.getCropRectRatio()
        val isCropping = transformHandler.isCroppingMode()

        // 获取真实的变换功能开关状态
        val isDragEnabled = transformHandler.isDragEnabled()
        val isScaleEnabled = transformHandler.isScaleEnabled()
        val isRotationEnabled = transformHandler.isRotationEnabled()

        // 获取窗口可见性状态
        val isVisible = transformHandler.isVisible

        // 获取镜像状态
        val isMirrored = transformHandler.isMirrorEnabled()

        // 获取圆角半径
        val cornerRadius = transformHandler.getCornerRadius()

        // 获取透明度
        val alpha = transformHandler.getWindowAlpha()

        // 获取调控状态
        val isControlEnabled = dataModule.getControlState(cameraId)



        // 获取边框状态
        val isBorderEnabled = transformHandler.isBorderEnabled()

        // 获取边框颜色
        val borderColor = transformHandler.getBorderColor()

        // 获取边框宽度
        val borderWidth = transformHandler.getBorderWidth()

        // 获取基础窗口尺寸
        val baseWindowWidth = transformHandler.getBaseWindowWidth()
        val baseWindowHeight = transformHandler.getBaseWindowHeight()

        // 获取备注
        val activity = dataModule.getCurrentActivity()
        val noteManager = com.example.castapp.utils.NoteManager(activity!!)
        val note = noteManager.getNote(cameraId)

        return CastWindowInfo(
            connectionId = cameraId,
            ipAddress = "本地设备",
            port = 0,
            isActive = true,
            deviceName = cameraName,
            note = note,
            positionX = positionX,
            positionY = positionY,
            scaleFactor = scaleFactor,
            rotationAngle = rotationAngle,
            zOrder = zOrder,
            isCropping = isCropping,
            cropRectRatio = cropRectRatio,
            isDragEnabled = isDragEnabled,
            isScaleEnabled = isScaleEnabled,
            isRotationEnabled = isRotationEnabled,
            isVisible = isVisible,
            isMirrored = isMirrored,
            cornerRadius = cornerRadius,
            alpha = alpha,
            isControlEnabled = isControlEnabled,
            isEditEnabled = false, // 📝 摄像头窗口不支持编辑功能

            isBorderEnabled = isBorderEnabled,
            borderColor = borderColor,
            borderWidth = borderWidth,
            baseWindowWidth = baseWindowWidth,
            baseWindowHeight = baseWindowHeight,
            isLandscapeModeEnabled = false // 🎯 摄像头窗口不支持横屏模式
        )
    }

    /**
     * 获取当前活跃的投屏窗口信息列表
     * 按照实际的视图层级顺序返回（从底层到顶层）
     */
    fun getActiveWindowInfoList(): List<CastWindowInfo> {
        val windowInfoList = mutableListOf<CastWindowInfo>()

        try {
            AppLog.d("【窗口管理】开始获取活跃窗口信息")
            AppLog.d("【窗口管理】当前窗口数量: ${dataModule.getWindowCount()}")
            AppLog.d("【窗口管理】当前连接IDs: ${dataModule.getAllConnectionIds()}")

            val activity = dataModule.getCurrentActivity() ?: return windowInfoList
            val container = dataModule.getSurfaceContainer() ?: return windowInfoList
            val stateManager = StateManager.getInstance(activity.application)
            val allConnections = stateManager.connections.value ?: emptyList()
            
            AppLog.d("【窗口管理】StateManager中的连接数量: ${allConnections.size}")
            allConnections.forEach { conn ->
                AppLog.d("【窗口管理】StateManager连接: ${conn.connectionId} -> ${conn.getDisplayText()}")
            }

            // 按照容器中的实际层级顺序获取窗口信息
            // 遍历容器中的所有子视图，按照从底层到顶层的顺序
            for (i in 0 until container.childCount) {
                val childView = container.getChildAt(i)

                // 查找对应的TransformHandler
                val connectionId = dataModule.getAllWindowMappings().entries.find { it.value == childView }?.key
                if (connectionId != null) {
                    val transformHandler = dataModule.getWindowMapping(connectionId)!!

                    try {
                        AppLog.d("【窗口管理】处理投屏窗口: $connectionId (层级索引: $i)")

                        // 检查窗口类型
                        val isCameraWindow = connectionId == "front_camera" || connectionId == "rear_camera"
                        val isMediaWindow = connectionId.startsWith("video_") || connectionId.startsWith("image_")
                        val isTextWindow = connectionId.startsWith("text_")

                        when {
                            isCameraWindow -> {
                                // 处理摄像头窗口
                                val cameraName = if (connectionId == "front_camera") "前置摄像头" else "后置摄像头"
                                val windowInfo = createCameraWindowInfo(connectionId, cameraName, transformHandler, i)
                                windowInfoList.add(windowInfo)
                                AppLog.d("【窗口管理】添加摄像头窗口信息: $cameraName (层级索引: $i)")
                            }
                            isMediaWindow -> {
                                // 处理媒体窗口
                                val mediaType = if (connectionId.startsWith("video_")) "视频" else "图片"
                                val windowInfo = createMediaWindowInfo(connectionId, mediaType, transformHandler, i)
                                windowInfoList.add(windowInfo)
                                AppLog.d("【窗口管理】添加媒体窗口信息: $mediaType (层级索引: $i)")
                            }
                            isTextWindow -> {
                                // 处理文本窗口
                                val textContent = getTextContentFromTransformHandler(transformHandler)
                                val windowInfo = createTextWindowInfo(connectionId, textContent, transformHandler, i)
                                windowInfoList.add(windowInfo)
                                AppLog.d("【窗口管理】添加文本窗口信息: $textContent (层级索引: $i)")
                            }
                            else -> {
                            // 通过连接ID查找连接信息
                            val connection = stateManager.findConnectionById(connectionId)
                            AppLog.d("【窗口管理】通过连接ID查找连接结果: $connection")

                            if (connection != null) {
                                // 获取设备名称
                                val deviceName = stateManager.getConnectionDeviceName(connection.connectionId)

                                // 获取变换信息（🎯 修复：使用容器位置而不是可见区域位置）
                                val positionX = transformHandler.getContainerDisplayX()
                                val positionY = transformHandler.getContainerDisplayY()
                                val scaleFactor = transformHandler.getCurrentScaleFactor()
                                val rotationAngle = transformHandler.getCurrentRotation()

                                // 获取裁剪区域参数和裁剪模式状态
                                val cropRectRatio = transformHandler.getCropRectRatio()
                                val isCropping = transformHandler.isCroppingMode()

                                // 获取真实的变换功能开关状态
                                val isDragEnabled = transformHandler.isDragEnabled()
                                val isScaleEnabled = transformHandler.isScaleEnabled()
                                val isRotationEnabled = transformHandler.isRotationEnabled()

                                // 获取窗口可见性状态
                                val isVisible = transformHandler.isVisible

                                // 获取镜像状态
                                val isMirrored = transformHandler.isMirrorEnabled()

                                // 获取圆角半径
                                val cornerRadius = transformHandler.getCornerRadius()

                                // 获取透明度
                                val alpha = transformHandler.getWindowAlpha()

                                // 获取调控状态
                                val isControlEnabled = dataModule.getControlState(connection.connectionId)



                                // 🎯 获取边框状态（修复边框开关状态不持久化问题）
                                val isBorderEnabled = transformHandler.isBorderEnabled()

                                // 获取边框颜色
                                val borderColor = transformHandler.getBorderColor()

                                // 获取边框宽度
                                val borderWidth = transformHandler.getBorderWidth()

                                // 🔧 添加边框状态调试日志
                                AppLog.d("【窗口管理】收集边框状态: $connectionId")
                                AppLog.d("  边框启用: $isBorderEnabled")
                                AppLog.d("  边框颜色: ${String.format("#%08X", borderColor)}")
                                AppLog.d("  边框宽度: ${borderWidth}dp")

                                // 获取基础窗口尺寸
                                val baseWindowWidth = transformHandler.getBaseWindowWidth()
                                val baseWindowHeight = transformHandler.getBaseWindowHeight()

                                // 🏷️ 获取备注
                                val noteManager = com.example.castapp.utils.NoteManager(activity)
                                val note = noteManager.getNote(connection.connectionId)

                                // 🎯 获取横屏模式状态（从CastingService获取实时状态）
                                val isLandscapeModeEnabled = getLandscapeModeEnabled(connection.connectionId)

                                val windowInfo = CastWindowInfo(
                                    connectionId = connection.connectionId,
                                    ipAddress = connection.ipAddress,
                                    port = connection.port,
                                    isActive = true,
                                    deviceName = deviceName,
                                    note = note, // 🏷️ 添加备注
                                    positionX = positionX,
                                    positionY = positionY,
                                    scaleFactor = scaleFactor,
                                    rotationAngle = rotationAngle,
                                    zOrder = i, // 使用容器中的索引作为层级参数（底层=0，顶层=最大值）
                                    isCropping = isCropping,
                                    cropRectRatio = cropRectRatio,
                                    isDragEnabled = isDragEnabled,
                                    isScaleEnabled = isScaleEnabled,
                                    isRotationEnabled = isRotationEnabled,
                                    isVisible = isVisible,
                                    isMirrored = isMirrored,
                                    cornerRadius = cornerRadius,
                                    alpha = alpha,
                                    isControlEnabled = isControlEnabled,
                                    isEditEnabled = false, // 📝 投屏窗口不支持编辑功能

                                    isBorderEnabled = isBorderEnabled, // 🎯 添加边框状态
                                    borderColor = borderColor, // 🎨 添加边框颜色
                                    borderWidth = borderWidth, // 🎯 添加边框宽度
                                    baseWindowWidth = baseWindowWidth,
                                    baseWindowHeight = baseWindowHeight,
                                    isLandscapeModeEnabled = isLandscapeModeEnabled // 🎯 添加横屏模式状态
                                )

                                windowInfoList.add(windowInfo)
                                AppLog.d("【窗口管理】成功添加窗口信息: ${windowInfo.getDisplayTextWithDevice()}")
                                AppLog.d("【窗口管理】变换信息: ${windowInfo.getTransformInfo()}")

                            } else {
                                AppLog.w("【窗口管理】无法找到连接ID对应的连接信息: $connectionId")

                                // 如果找不到连接信息，创建一个临时的窗口信息（🎯 修复：使用容器位置而不是可见区域位置）
                                val positionX = transformHandler.getContainerDisplayX()
                                val positionY = transformHandler.getContainerDisplayY()
                                val scaleFactor = transformHandler.getCurrentScaleFactor()
                                val rotationAngle = transformHandler.getCurrentRotation()

                                // 获取裁剪模式状态
                                val isCropping = transformHandler.isCroppingMode()

                                // 获取功能开关状态
                                val isDragEnabled = transformHandler.isDragEnabled()
                                val isScaleEnabled = transformHandler.isScaleEnabled()
                                val isRotationEnabled = transformHandler.isRotationEnabled()

                                // 获取窗口可见性状态
                                val isVisible = transformHandler.isVisible



                                // 🎯 获取边框状态（修复边框开关状态不持久化问题）
                                val isBorderEnabled = transformHandler.isBorderEnabled()

                                // 获取边框颜色
                                val borderColor = transformHandler.getBorderColor()

                                // 获取边框宽度
                                val borderWidth = transformHandler.getBorderWidth()

                                // 🔧 添加边框状态调试日志（临时窗口）
                                AppLog.d("【窗口管理】收集边框状态（临时窗口）: $connectionId")
                                AppLog.d("  边框启用: $isBorderEnabled")
                                AppLog.d("  边框颜色: ${String.format("#%08X", borderColor)}")
                                AppLog.d("  边框宽度: ${borderWidth}dp")

                                // 获取基础窗口尺寸
                                val baseWindowWidth = transformHandler.getBaseWindowWidth()
                                val baseWindowHeight = transformHandler.getBaseWindowHeight()

                                val tempWindowInfo = CastWindowInfo(
                                    connectionId = connectionId,
                                    ipAddress = "未知",
                                    port = 0,
                                    isActive = true,
                                    positionX = positionX,
                                    positionY = positionY,
                                    scaleFactor = scaleFactor,
                                    rotationAngle = rotationAngle,
                                    zOrder = i, // 临时窗口也需要层级参数
                                    isCropping = isCropping,
                                    isDragEnabled = isDragEnabled,
                                    isScaleEnabled = isScaleEnabled,
                                    isRotationEnabled = isRotationEnabled,
                                    isVisible = isVisible,
                                    isEditEnabled = false, // 📝 临时窗口不支持编辑功能

                                    isBorderEnabled = isBorderEnabled, // 🎯 添加边框状态
                                    borderColor = borderColor, // 🎨 添加边框颜色
                                    borderWidth = borderWidth, // 🎯 添加边框宽度
                                    baseWindowWidth = baseWindowWidth,
                                    baseWindowHeight = baseWindowHeight,
                                    isLandscapeModeEnabled = false // 🎯 临时窗口不支持横屏模式
                                )
                                windowInfoList.add(tempWindowInfo)
                                AppLog.d("【窗口管理】添加临时窗口信息: $connectionId (层级索引: $i)")
                            }
                        }
                    }

                    } catch (e: Exception) {
                        AppLog.e("【窗口管理】处理投屏窗口信息失败: $connectionId", e)
                    }
                }
            }

            // 反转列表，使得列表顶部显示最上层的窗口（符合用户直觉）
            windowInfoList.reverse()

            // 重新设置层级数值：最顶层显示为1，每下一层增加1
            val correctedWindowInfoList = windowInfoList.mapIndexed { index, windowInfo ->
                windowInfo.copy(zOrder = index + 1) // 顶部窗口(index=0)显示为层级1
            }

            AppLog.d("【窗口管理】最终获取 ${correctedWindowInfoList.size} 个窗口信息，已按实际层级排序")
            correctedWindowInfoList.forEachIndexed { index, windowInfo ->
                AppLog.d("【窗口管理】显示位置 $index -> 层级 ${windowInfo.zOrder}: ${windowInfo.getDisplayTextWithDevice()}")
            }

            return correctedWindowInfoList

        } catch (e: Exception) {
            AppLog.e("【窗口管理】获取活跃窗口信息失败", e)
            // 即使异常也要返回修正后的列表
            windowInfoList.reverse()
            return windowInfoList.mapIndexed { index, windowInfo ->
                windowInfo.copy(zOrder = index + 1)
            }
        }
    }
    
    /**
     * 获取当前活跃的投屏窗口信息列表（公共方法）
     */
    fun getCurrentWindowInfoList(): List<CastWindowInfo> {
        return getActiveWindowInfoList()
    }

    /**
     * 🎯 获取横屏模式启用状态
     * 从WindowSettingsManager获取本地存储的横屏状态
     */
    private fun getLandscapeModeEnabled(connectionId: String): Boolean {
        return try {
            // 🎯 从WindowSettingsManager获取横屏状态
            val windowSettingsManager = com.example.castapp.manager.WindowSettingsManager.getInstance()
            val isEnabled = windowSettingsManager.getLandscapeModeEnabled(connectionId)

            AppLog.d("🎯 获取横屏模式状态: $connectionId -> $isEnabled")
            return isEnabled

        } catch (e: Exception) {
            AppLog.e("🎯 获取横屏模式状态失败: $connectionId", e)
            false
        }
    }

    /**
     * 从TransformHandler获取文本内容
     */
    private fun getTextContentFromTransformHandler(transformHandler: com.example.castapp.ui.windowsettings.TransformHandler): String {
        return try {
            // 尝试通过反射或其他方式获取文本内容
            // 这里简化处理，使用连接ID中的信息或默认值
            val connectionId = transformHandler.getConnectionId()
            if (connectionId.startsWith("text_")) {
                // 从TextFormatManager获取保存的文本内容
                val activity = dataModule.getCurrentActivity()
                if (activity != null) {
                    val textFormatManager = com.example.castapp.utils.TextFormatManager(activity)
                    val formatInfo = textFormatManager.getTextFormat(connectionId)
                    formatInfo?.textContent ?: "文本内容"
                } else {
                    "文本内容"
                }
            } else {
                "文本内容"
            }
        } catch (e: Exception) {
            AppLog.e("【窗口信息模块】获取文本内容失败", e)
            "文本内容"
        }
    }
}
