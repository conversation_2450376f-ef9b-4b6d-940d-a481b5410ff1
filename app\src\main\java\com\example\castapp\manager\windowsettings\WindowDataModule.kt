package com.example.castapp.manager.windowsettings

import android.app.Activity
import android.widget.FrameLayout
import com.example.castapp.ui.windowsettings.TransformHandler
import com.example.castapp.ui.dialog.WindowManagerDialog
import java.util.concurrent.ConcurrentHashMap
import java.lang.ref.WeakReference
import com.example.castapp.utils.AppLog

/**
 * 窗口数据模块
 * 负责管理所有投屏窗口相关的数据存储和映射关系
 */
class WindowDataModule {
    
    // 存储连接ID到可拖动窗口的映射
    private val connectionCastWindows = ConcurrentHashMap<String, TransformHandler>()
    
    // 存储连接ID到屏幕分辨率的映射
    private val connectionScreenResolutions = ConcurrentHashMap<String, Pair<Int, Int>>()
    
    // 存储等待创建窗口的连接ID（等待正确分辨率）
    private val pendingWindowCreations = ConcurrentHashMap.newKeySet<String>()

    // 存储连接ID到调控状态的映射
    private val connectionControlStates = ConcurrentHashMap<String, Boolean>()

    // 📝 存储连接ID到编辑状态的映射（仅文本窗口）
    private val connectionEditStates = ConcurrentHashMap<String, Boolean>()

    // 保存当前显示的窗口管理对话框引用
    private var currentWindowManagerDialog: WindowManagerDialog? = null
    
    // 当前Activity和容器的弱引用
    private var currentActivityRef: WeakReference<Activity>? = null
    private var surfaceContainerRef: WeakReference<FrameLayout>? = null
    
    /**
     * 初始化数据模块
     */
    fun initialize(activity: Activity, container: FrameLayout) {
        this.currentActivityRef = WeakReference(activity)
        this.surfaceContainerRef = WeakReference(container)
        AppLog.d("WindowDataModule初始化完成")
    }
    
    // ==================== 窗口映射管理 ====================
    
    /**
     * 添加窗口映射
     */
    fun addWindowMapping(connectionId: String, transformHandler: TransformHandler) {
        connectionCastWindows[connectionId] = transformHandler
        AppLog.d("添加窗口映射: $connectionId")
    }
    
    /**
     * 移除窗口映射
     */
    fun removeWindowMapping(connectionId: String): TransformHandler? {
        val removed = connectionCastWindows.remove(connectionId)
        AppLog.d("移除窗口映射: $connectionId")
        return removed
    }
    
    /**
     * 获取窗口映射
     */
    fun getWindowMapping(connectionId: String): TransformHandler? {
        return connectionCastWindows[connectionId]
    }
    
    /**
     * 检查窗口是否存在
     */
    fun containsWindow(connectionId: String): Boolean {
        return connectionCastWindows.containsKey(connectionId)
    }
    
    /**
     * 获取所有窗口映射
     */
    fun getAllWindowMappings(): Map<String, TransformHandler> {
        return connectionCastWindows.toMap()
    }
    
    /**
     * 获取所有连接ID
     */
    fun getAllConnectionIds(): Set<String> {
        return connectionCastWindows.keys.toSet()
    }
    
    /**
     * 获取窗口数量
     */
    fun getWindowCount(): Int {
        return connectionCastWindows.size
    }
    
    /**
     * 清空所有窗口映射
     */
    fun clearAllWindowMappings() {
        connectionCastWindows.clear()
        AppLog.d("清空所有窗口映射")
    }
    
    // ==================== 分辨率映射管理 ====================
    
    /**
     * 设置屏幕分辨率
     */
    fun setScreenResolution(connectionId: String, width: Int, height: Int) {
        connectionScreenResolutions[connectionId] = Pair(width, height)
        AppLog.d("设置屏幕分辨率: $connectionId ${width}x${height}")
    }
    
    /**
     * 获取屏幕分辨率
     */
    fun getScreenResolution(connectionId: String): Pair<Int, Int>? {
        return connectionScreenResolutions[connectionId]
    }
    
    /**
     * 移除屏幕分辨率
     */
    fun removeScreenResolution(connectionId: String) {
        connectionScreenResolutions.remove(connectionId)
        AppLog.d("移除屏幕分辨率: $connectionId")
    }
    
    /**
     * 清空所有分辨率映射
     */
    fun clearAllScreenResolutions() {
        connectionScreenResolutions.clear()
        AppLog.d("清空所有分辨率映射")
    }
    
    // ==================== 等待创建管理 ====================
    
    /**
     * 添加等待创建的连接
     */
    fun addPendingCreation(connectionId: String) {
        pendingWindowCreations.add(connectionId)
        AppLog.d("添加等待创建: $connectionId")
    }
    
    /**
     * 移除等待创建的连接
     */
    fun removePendingCreation(connectionId: String): Boolean {
        val removed = pendingWindowCreations.remove(connectionId)
        AppLog.d("移除等待创建: $connectionId")
        return removed
    }
    
    /**
     * 检查是否在等待创建
     */
    fun isPendingCreation(connectionId: String): Boolean {
        return pendingWindowCreations.contains(connectionId)
    }
    
    /**
     * 清空所有等待创建
     */
    fun clearAllPendingCreations() {
        pendingWindowCreations.clear()
        AppLog.d("清空所有等待创建")
    }
    
    // ==================== 调控状态管理 ====================
    
    /**
     * 设置调控状态
     */
    fun setControlState(connectionId: String, isEnabled: Boolean) {
        connectionControlStates[connectionId] = isEnabled
        AppLog.d("设置调控状态: $connectionId -> $isEnabled")
    }
    
    /**
     * 获取调控状态
     */
    fun getControlState(connectionId: String): Boolean {
        return connectionControlStates[connectionId] ?: false
    }
    
    /**
     * 移除调控状态
     */
    fun removeControlState(connectionId: String) {
        connectionControlStates.remove(connectionId)
        AppLog.d("移除调控状态: $connectionId")
    }
    
    /**
     * 清空所有调控状态
     */
    fun clearAllControlStates() {
        connectionControlStates.clear()
        AppLog.d("清空所有调控状态")
    }

    // ==================== 📝 编辑状态管理 ====================

    /**
     * 设置编辑状态（仅文本窗口）
     */
    fun setEditState(connectionId: String, isEnabled: Boolean) {
        connectionEditStates[connectionId] = isEnabled
        AppLog.d("【编辑状态】设置编辑状态: $connectionId -> $isEnabled")

        // 📝 保存到SharedPreferences
        saveEditStateToPreferences(connectionId, isEnabled)
    }

    /**
     * 获取编辑状态（仅文本窗口）
     */
    fun getEditState(connectionId: String): Boolean {
        // 如果内存中没有状态，尝试从SharedPreferences恢复
        return connectionEditStates[connectionId] ?: loadEditStateFromPreferences(connectionId)
    }

    /**
     * 移除编辑状态
     */
    fun removeEditState(connectionId: String) {
        connectionEditStates.remove(connectionId)
        clearEditStateFromPreferences(connectionId)
        AppLog.d("【编辑状态】移除编辑状态: $connectionId")
    }

    /**
     * 清空所有编辑状态
     */
    fun clearAllEditStates() {
        connectionEditStates.clear()
        AppLog.d("【编辑状态】清空所有编辑状态")
    }

    /**
     * 📝 保存编辑状态到SharedPreferences
     */
    private fun saveEditStateToPreferences(connectionId: String, isEnabled: Boolean) {
        try {
            val activity = getCurrentActivity() ?: return
            val sharedPrefs = activity.getSharedPreferences("window_edit_states", android.content.Context.MODE_PRIVATE)
            sharedPrefs.edit().apply {
                putBoolean("edit_enabled_$connectionId", isEnabled)
                apply()
            }
            AppLog.d("【编辑状态】已保存到SharedPreferences: $connectionId -> $isEnabled")
        } catch (e: Exception) {
            AppLog.e("【编辑状态】保存到SharedPreferences失败: $connectionId", e)
        }
    }

    /**
     * 📝 从SharedPreferences恢复编辑状态
     */
    fun loadEditStateFromPreferences(connectionId: String): Boolean {
        return try {
            val activity = getCurrentActivity() ?: return false
            val sharedPrefs = activity.getSharedPreferences("window_edit_states", android.content.Context.MODE_PRIVATE)
            val isEnabled = sharedPrefs.getBoolean("edit_enabled_$connectionId", false)

            // 恢复到内存中
            connectionEditStates[connectionId] = isEnabled
            AppLog.d("【编辑状态】从SharedPreferences恢复: $connectionId -> $isEnabled")

            isEnabled
        } catch (e: Exception) {
            AppLog.e("【编辑状态】从SharedPreferences恢复失败: $connectionId", e)
            false
        }
    }

    /**
     * 📝 清理指定连接的编辑状态持久化数据
     */
    fun clearEditStateFromPreferences(connectionId: String) {
        try {
            val activity = getCurrentActivity() ?: return
            val sharedPrefs = activity.getSharedPreferences("window_edit_states", android.content.Context.MODE_PRIVATE)
            sharedPrefs.edit().apply {
                remove("edit_enabled_$connectionId")
                apply()
            }
            AppLog.d("【编辑状态】已从SharedPreferences清理: $connectionId")
        } catch (e: Exception) {
            AppLog.e("【编辑状态】从SharedPreferences清理失败: $connectionId", e)
        }
    }
    
    // ==================== 对话框管理 ====================
    
    /**
     * 设置当前窗口管理对话框
     */
    fun setCurrentDialog(dialog: WindowManagerDialog?) {
        currentWindowManagerDialog = dialog
        AppLog.d("设置当前对话框: ${dialog != null}")
    }
    
    /**
     * 获取当前窗口管理对话框
     */
    fun getCurrentDialog(): WindowManagerDialog? {
        return currentWindowManagerDialog
    }
    
    // ==================== Activity和容器引用管理 ====================
    
    /**
     * 获取当前Activity
     */
    fun getCurrentActivity(): Activity? {
        return currentActivityRef?.get()
    }
    
    /**
     * 获取Surface容器
     */
    fun getSurfaceContainer(): FrameLayout? {
        return surfaceContainerRef?.get()
    }
    
    // ==================== 清理方法 ====================
    
    /**
     * 清理所有数据
     */
    fun cleanup() {
        clearAllWindowMappings()
        clearAllScreenResolutions()
        clearAllPendingCreations()
        clearAllControlStates()
        currentWindowManagerDialog = null
        currentActivityRef = null
        surfaceContainerRef = null
        AppLog.d("WindowDataModule清理完成")
    }
}
