package com.example.castapp.ui.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.example.castapp.R
import com.example.castapp.model.CastWindowInfo
import com.example.castapp.ui.adapter.WindowManagerAdapter
import com.example.castapp.utils.AppLog

/**
 * 窗口管理BottomSheet对话框
 * 显示当前所有活跃的投屏窗口信息
 */
class WindowManagerDialog(
    private val windowInfoProvider: () -> List<CastWindowInfo>,
    private val onCropSwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onDragSwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onScaleSwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onRotationSwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onVisibilitySwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onMirrorSwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onCornerRadiusChanged: ((String, Float) -> Unit)? = null,
    private val onAlphaChanged: ((String, Float) -> Unit)? = null,
    private val onControlSwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onEditSwitchChanged: ((String, Boolean) -> Unit)? = null, // 📝 编辑开关回调

    private val onBorderSwitchChanged: ((String, Boolean) -> Unit)? = null,
    private val onBorderColorChanged: ((String, Int) -> Unit)? = null,
    private val onBorderWidthChanged: ((String, Float) -> Unit)? = null,
    private val onNoteChanged: ((String, String) -> Unit)? = null, // 🏷️ 备注变更回调
    private val onWindowDeleted: ((CastWindowInfo) -> Unit)? = null, // 🗑️ 窗口删除回调
    private val onVideoPlaySwitchChanged: ((String, Boolean) -> Unit)? = null, // 🎬 视频播放开关回调
    private val onVideoLoopCountChanged: ((String, Int) -> Unit)? = null, // 🎬 视频循环次数回调
    private val onVideoVolumeChanged: ((String, Int) -> Unit)? = null, // 🎬 视频音量回调
    private val onLandscapeSwitchChanged: ((String, Boolean) -> Unit)? = null // 🎯 横屏模式控制回调
) : BottomSheetDialogFragment() {

    // 对话框关闭回调
    var onDialogDismissed: (() -> Unit)? = null

    private lateinit var tvWindowCount: TextView
    private lateinit var rvCastWindows: RecyclerView
    private lateinit var layoutEmptyState: LinearLayout
    private lateinit var btnClose: ImageView

    private lateinit var adapter: WindowManagerAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_window_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initViews(view)
        setupRecyclerView()
        setupClickListeners()
        refreshWindowList()

        AppLog.d("窗口管理BottomSheet创建完成")
    }

    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        tvWindowCount = view.findViewById(R.id.tv_window_count)
        rvCastWindows = view.findViewById(R.id.rv_cast_windows)
        layoutEmptyState = view.findViewById(R.id.layout_empty_state)
        btnClose = view.findViewById(R.id.btn_close)

        // 🔧 隐藏同步开关 - 接收端不需要同步开关，该功能只为遥控端服务
        val syncControlSwitch = view.findViewById<View>(R.id.switch_sync_control)
        val syncControlLayout = syncControlSwitch?.parent as? LinearLayout
        syncControlLayout?.visibility = View.GONE
        AppLog.d("【接收端窗口管理】已隐藏同步开关，该功能只为遥控端服务")
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = WindowManagerAdapter()
        rvCastWindows.layoutManager = LinearLayoutManager(context)
        rvCastWindows.adapter = adapter



        // 设置裁剪开关监听器
        adapter.setOnCropSwitchListener(object : WindowManagerAdapter.OnCropSwitchListener {
            override fun onCropSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【裁剪开关】连接 $connectionId 裁剪状态变更为: $isEnabled")
                onCropSwitchChanged?.invoke(connectionId, isEnabled)
            }
        })

        // 设置变换功能开关监听器
        adapter.setOnTransformSwitchListener(object : WindowManagerAdapter.OnTransformSwitchListener {
            override fun onDragSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【拖动开关】连接 $connectionId 拖动功能变更为: $isEnabled")
                onDragSwitchChanged?.invoke(connectionId, isEnabled)
            }

            override fun onScaleSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【缩放开关】连接 $connectionId 缩放功能变更为: $isEnabled")
                onScaleSwitchChanged?.invoke(connectionId, isEnabled)
            }

            override fun onRotationSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【旋转开关】连接 $connectionId 旋转功能变更为: $isEnabled")
                onRotationSwitchChanged?.invoke(connectionId, isEnabled)
            }
        })

        // 设置隐藏开关监听器
        adapter.setOnVisibilitySwitchListener(object : WindowManagerAdapter.OnVisibilitySwitchListener {
            override fun onVisibilitySwitchChanged(connectionId: String, isVisible: Boolean) {
                AppLog.d("【隐藏开关】连接 $connectionId 可见性变更为: $isVisible")
                onVisibilitySwitchChanged?.invoke(connectionId, isVisible)
            }
        })

        // 设置镜像开关监听器
        adapter.setOnMirrorSwitchListener(object : WindowManagerAdapter.OnMirrorSwitchListener {
            override fun onMirrorSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【镜像开关】连接 $connectionId 镜像功能变更为: $isEnabled")
                onMirrorSwitchChanged?.invoke(connectionId, isEnabled)
            }
        })

        // 设置圆角变化监听器
        adapter.setOnCornerRadiusChangeListener(object : WindowManagerAdapter.OnCornerRadiusChangeListener {
            override fun onCornerRadiusChanged(connectionId: String, cornerRadius: Float) {
                AppLog.d("【圆角滑动条】连接 $connectionId 圆角半径变更为: ${cornerRadius}dp")
                onCornerRadiusChanged?.invoke(connectionId, cornerRadius)
            }
        })

        // 设置透明度变化监听器
        adapter.setOnAlphaChangeListener(object : WindowManagerAdapter.OnAlphaChangeListener {
            override fun onAlphaChanged(connectionId: String, alpha: Float) {
                AppLog.d("【透明度滑动条】连接 $connectionId 透明度变更为: ${(alpha * 100).toInt()}%")
                onAlphaChanged?.invoke(connectionId, alpha)
            }
        })

        // 设置调控开关监听器
        adapter.setOnControlSwitchListener(object : WindowManagerAdapter.OnControlSwitchListener {
            override fun onControlSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【调控开关】连接 $connectionId 精准调控功能变更为: $isEnabled")
                onControlSwitchChanged?.invoke(connectionId, isEnabled)
            }
        })

        // 📝 设置编辑开关监听器
        adapter.setOnEditSwitchListener(object : WindowManagerAdapter.OnEditSwitchListener {
            override fun onEditSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【编辑开关】连接 $connectionId 文本编辑功能变更为: $isEnabled")
                onEditSwitchChanged?.invoke(connectionId, isEnabled)
            }
        })

        // 设置边框开关监听器
        adapter.setOnBorderSwitchListener(object : WindowManagerAdapter.OnBorderSwitchListener {
            override fun onBorderSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【边框开关】连接 $connectionId 边框显示变更为: $isEnabled")
                onBorderSwitchChanged?.invoke(connectionId, isEnabled)
            }
        })

        // 设置边框颜色变化监听器
        adapter.setOnBorderColorChangeListener(object : WindowManagerAdapter.OnBorderColorChangeListener {
            override fun onBorderColorChanged(connectionId: String, color: Int) {
                AppLog.d("【边框颜色】连接 $connectionId 边框颜色变更为: ${String.format("#%06X", 0xFFFFFF and color)}")
                onBorderColorChanged?.invoke(connectionId, color)
            }
        })

        // 设置边框宽度变化监听器
        adapter.setOnBorderWidthChangeListener(object : WindowManagerAdapter.OnBorderWidthChangeListener {
            override fun onBorderWidthChanged(connectionId: String, width: Float) {
                AppLog.d("【边框宽度】连接 $connectionId 边框宽度变更为: ${width}dp")
                onBorderWidthChanged?.invoke(connectionId, width)
            }
        })

        // 🏷️ 设置备注变更监听器
        adapter.setOnNoteChangeListener(object : WindowManagerAdapter.OnNoteChangeListener {
            override fun onNoteChanged(connectionId: String, note: String) {
                AppLog.d("【备注】连接 $connectionId 备注变更为: $note")
                onNoteChanged?.invoke(connectionId, note)
            }
        })

        // 🗑️ 设置窗口删除监听器
        adapter.setOnWindowDeleteListener(object : WindowManagerAdapter.OnWindowDeleteListener {
            override fun onWindowDelete(windowInfo: CastWindowInfo) {
                AppLog.d("【删除】用户确认删除窗口: ${windowInfo.connectionId}")
                onWindowDeleted?.invoke(windowInfo)
            }
        })

        // 🎬 设置视频控制监听器
        adapter.setOnVideoControlListener(object : WindowManagerAdapter.OnVideoControlListener {
            override fun onVideoPlaySwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【视频控制】播放开关: $connectionId = $isEnabled")
                onVideoPlaySwitchChanged?.invoke(connectionId, isEnabled)
            }

            override fun onVideoLoopCountChanged(connectionId: String, loopCount: Int) {
                AppLog.d("【视频控制】循环次数: $connectionId = $loopCount")
                onVideoLoopCountChanged?.invoke(connectionId, loopCount)
            }

            override fun onVideoVolumeChanged(connectionId: String, volume: Int) {
                AppLog.d("【视频控制】音量调整: $connectionId = ${volume}%")
                onVideoVolumeChanged?.invoke(connectionId, volume)
            }
        })

        // 🎯 设置横屏模式控制监听器
        adapter.setOnLandscapeSwitchListener(object : WindowManagerAdapter.OnLandscapeSwitchListener {
            override fun onLandscapeSwitchChanged(connectionId: String, isEnabled: Boolean) {
                AppLog.d("【横屏开关】连接 $connectionId 横屏模式变更为: $isEnabled")
                onLandscapeSwitchChanged?.invoke(connectionId, isEnabled)
            }
        })

    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        btnClose.setOnClickListener {
            AppLog.d("【BottomSheet】用户点击关闭按钮")
            dismiss()
        }
    }

    /**
     * 刷新窗口列表
     */
    fun refreshWindowList() {
        try {
            AppLog.d("【BottomSheet】开始刷新窗口列表")
            val windowInfoList = windowInfoProvider()

            AppLog.d("【BottomSheet】获取到 ${windowInfoList.size} 个窗口")
            windowInfoList.forEachIndexed { index, windowInfo ->
                AppLog.d("【BottomSheet】窗口信息 位置$index -> 序号${index + 1}, zOrder=${windowInfo.zOrder}: ${windowInfo.getDisplayTextWithDevice()}")
                AppLog.d("【BottomSheet】变换信息: ${windowInfo.getTransformInfo()}")
            }

            // 更新窗口数量显示
            tvWindowCount.text = "${windowInfoList.size}个窗口"

            // 🐾 强制更新列表，确保序号正确显示
            adapter.submitList(windowInfoList.toList()) // 提交新列表

            // 🐾 延迟强制刷新，确保所有序号正确更新
            rvCastWindows.post {
                AppLog.d("【BottomSheet】执行强制刷新，确保序号正确")
                adapter.notifyDataSetChanged() // 强制刷新所有item
            }

            // 显示/隐藏空状态
            if (windowInfoList.isEmpty()) {
                rvCastWindows.visibility = View.GONE
                layoutEmptyState.visibility = View.VISIBLE
                AppLog.d("【BottomSheet】显示空状态")
            } else {
                rvCastWindows.visibility = View.VISIBLE
                layoutEmptyState.visibility = View.GONE
                AppLog.d("【BottomSheet】显示窗口列表")


            }

        } catch (e: Exception) {
            AppLog.e("【BottomSheet】刷新窗口列表失败", e)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDialogDismissed?.invoke()
        AppLog.d("【BottomSheet】窗口管理对话框已关闭")
    }
}
